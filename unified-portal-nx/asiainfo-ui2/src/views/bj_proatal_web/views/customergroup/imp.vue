<template>
  <div>
    <div class="formbox" style="position:relative">
      <el-form ref="queryForm" :inline="true" :model="formInline" class="demo-form-inline" label-position="right">

        <el-form-item label="区域" prop="gridId">
          <el-cascader
            v-model="formInline.gridId"
            :options="optionsCascader"
            :size="conditionsize"
            clearable
            :props="{
              checkStrictly:true
            }"
            :show-all-levels="false"
          />
        </el-form-item>
        <el-form-item label="客群状态" prop="careStatus">
          <el-select v-model="formInline.careStatus" placeholder="请选择" :size="conditionsize">
            <el-option :label="impActiveName=='1'?'已关怀':'已修复'" value="0" />
            <el-option :label="impActiveName=='1'?'未关怀':'未修复'" value="1" />
          </el-select>
        </el-form-item>

        <el-form-item label="创建时间" class="timeRange">
          <el-date-picker
            v-model="formInline.timeRange"
            style="width:230px"
            :size="conditionsize"
            type="daterange"
            popper-class="xps"
            range-separator="~"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />

        </el-form-item>

        <el-form-item label="客群名称" prop="customerName">
          <el-input
            v-model="formInline.customerName"
            :size="conditionsize"
            placeholder="请输入客群名称搜索"
          >
            <!-- <el-button slot="append" @click="query">搜索</el-button> -->
          </el-input>
        </el-form-item>

        <el-form-item>
          <el-button :size="conditionsize" type="primary" @click="queryList">查询</el-button>
          <el-button :size="conditionsize" type="default" @click="resetForm('queryForm')">重置</el-button>
        </el-form-item>

      </el-form>
      <div style="margin-bottom:10px;text-align:right">
        <el-button size="small" @click="drawer=true">生成客群</el-button>
      </div>
    </div>

    <div class="conbox" style="background:#fff;">
      <div>
        <el-table
          v-loading="tableLoading"
          :data="tableDatas"
          style="width: 100%"
          max-height="560"
        >
          <el-table-column
            v-for="item in columnList"
            :key="item.name"
            :prop="item.key"
            :label="item.name"
            :width="item.width?item.width:'unset'"
            align="center"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span v-if="item.key=='careStatus'">
                <span v-if="impActiveName==1">
                  {{ scope.row[item.key]==1?'待关怀':scope.row[item.key]==0?'已关怀':'' }}
                </span>
                <span v-else>
                  {{ scope.row[item.key]==1?'待修复':scope.row[item.key]==0?'已修复':'' }}
                </span>

              </span>
              <span v-else-if="item.key=='isSend'">
                {{ scope.row[item.key]=='0'?'已发送':scope.row[item.key]=='1'?'未发送':'' }}
              </span>
              <span v-else-if="item.key=='isPush'">
                <!-- 1-未推送 2-推送中 3-已推送 -->
                {{ scope.row[item.key]=='1'?'未推送':scope.row[item.key]=='2'?'推送中':scope.row[item.key]=='3'?'已推送':'' }}
              </span>
              <span v-else-if="item.key=='customerCategory'">
                <!-- 1-未推送 2-推送中 3-已推送 -->
                {{ scope.row[item.key]=='1'?'关怀客群':scope.row[item.key]=='2'?'修复客群':'' }}
              </span>

              <span v-else>{{ scope.row[item.key] }}</span>
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            align="center"
            width="120"
          >
            <template slot-scope="scope">
              <div class="optionbtnbox">
                <div>
                  <span class="coloryellow" @click="down(scope.row)">下载</span>
                </div>
                <div>

                <el-popconfirm
                    confirm-button-text="删除"
                    cancel-button-text="取消"
                    icon="el-icon-info"
                    icon-color="red"
                    title="确定删除本条数据？"
                    @confirm="dele(scope.row)"
                  >
                    <span slot="reference" class="coloryellow">删除</span>
                  </el-popconfirm>

                </div>

              </div>
            </template>
          </el-table-column>

        </el-table>

      </div>

    </div>
    <!-- 分页功能 -->
    <div style="padding:10px 0;background:#fff;">
      <el-pagination
        v-if="tableDatas.length"
        :current-page="page.current"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.total"
        @size-change="sizeChange"
        @current-change="pageCurrentChange"
      />
    </div>

    <el-dialog title="查看" :visible.sync="dialogTableVisible" :append-to-body="true" width="75%" @close="closeDetailDialog">
      <div style="padding-bottom:10px">
        <span class="la">修复时间：</span>
        <span class="vl" style="margin-right:40px">{{ activeRow.repairTime||'-' }}</span>
        <span class="la">电话号码：</span>
        <span class="vl">{{ activeRow.servNumber||'-' }}</span>
      </div>
      <el-table v-loading="detailTableLoading" :data="dialogTableData">
        <el-table-column
          v-for="item in detailColumnList"
          :key="item.name"
          :prop="item.key"
          :label="item.name"
          :width="item.width?item.width:'unset'"
          align="center"
          :show-overflow-tooltip="true"
        /></el-table-column></el-table>
      <!-- 详情的分页功能 -->
      <div style="padding:10px 0;background:#fff;text-align:right">
        <el-pagination
          v-if="dialogTableData.length"
          :current-page="detailPage.current"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="detailPage.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="detailPage.total"
          @size-change="detailSizeChange"
          @current-change="detailPageCurrentChange"
        />
      </div>

    </el-dialog>
    <el-drawer
      :append-to-body="true"
      :visible.sync="drawer"
      size="50%"
    >
      <div slot="title">
        <span style="font-weight:bold;font-size:18px;color:black;">生成客群</span>
      </div>
      <customerAddForm v-if="drawer" :data_source="data_source" :attribute_style="attribute_style" :tree-data="treeLabelData" :options-cascader="optionsCascader" @closedrawer="closeDrawer" />
    </el-drawer>

  </div>
</template>
<script>
import { getAllGrides, queryCustomerPageList, getFieldEnumImp, labelLists, deleteCsmMulti, viewSingleCustomerList, downloadCustomerInfo } from '@/api/customer/index'

import tool from '@/views/bj_proatal_web/utils/utils'
import customerAddForm from './../../components/common/DataCustomerAddForm.vue'

export default {
  components: {
    customerAddForm
  },
  props: {
    impActiveName: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      treeLabelData: [],
      drawer: false,
      dialogTableData: [],
      dialogTableVisible: false,
      tableDatas: [],
      tableLoading: false,
      conditionsize: 'small',
      //          city  地市
      // district 区县
      // grid 网格
      optionsCascader: [],
      formInline: {
        customerCategory: '1', // '1关怀客群' '2'修复客群
        careStatus: '',
        customerName: '',
        gridId: [],
        timeRange: []

      },
      page: {
        current: 1,
        size: 10,
        total: 0
      },
      detailPage: {
        'currentPage': 1,
        'pageSize': 10,
        'total': 0
      },
      optionsCascaderExpand: [],
      columnList: [

        { key: 'code', name: '客群编码', width: 120 },
        { key: 'customerName', name: '客群名称', width: 120 },
        { key: 'city', name: '归属地市' },
        { key: 'district', name: '归属区县' },
        { key: 'grid', name: '归属网格' },
        { key: 'satisfiedType', name: '满意度类型', width: 100 },
        { key: 'attributeStyle', name: '属性摸排方式', width: 110 },
        { key: 'cusAttribute', name: '得分(客户属性)', width: 110 },
        { key: 'customerButton', name: '客户按键' },
        { key: 'ifGroup', name: '是否集团客户', width: 110 },
        { key: 'dataSource', name: '修复数据来源渠道', width: 130 },
        { key: 'customerCategory', name: '客群类别' },
        { key: 'createUserId', name: '创建人id' },
        { key: 'createUserName', name: '创建人名称', width: 100 },
        { key: 'number', name: '客群数量' },
        { key: 'careStatus', name: '状态' },
        { key: 'createTime', name: '创建时间', width: 100 },

        { key: 'isSend', name: '是否发送短信', width: 120 },
        { key: 'isPush', name: '是否推送' }
      ],
      detailColumnList: [
        { key: 'userTellPhone', name: '客户告知号码' },
        { key: 'cusAttribute', name: '分数(客户属性)' },
        { key: 'attributeStyle', name: '属性摸排方式' },
        { key: 'appraiseTime', name: '参评时间' },
        { key: 'repairTime', name: '修复时间' },
        { key: 'storageTime', name: '入库时间' },
        { key: 'dataSource', name: '修复来源渠道' },
        { key: 'dataSourceData', name: '数据来源日期' },
        { key: 'satisfiedType', name: '满意度类型' }

      ],
      detailTableLoading: false,
      activeRow: {},
      attribute_style: [],
      data_source: []
    }
  },
  watch: {
    impActiveName(v, oldv) {
      console.log('v==>', v)
      this.queryList()
    }
  },
  created() {
    this.labelLists()
    this.getCascaderOptions()
    this.getParamsOpts('attribute_style')
    this.getParamsOpts('data_source')
  },

  mounted() {
    this.query()
  },

  methods: {

    closeDrawer() {
      this.drawer = false
      this.query()
    },
    // 获取字段的枚举值下拉
    getParamsOpts(a) {
      getFieldEnumImp(a).then(res => {
        const { data } = res
        if (data && Array.isArray(data)) {
          this[a] = data
        }
      })
    },
    // 获取所有标签
    labelLists() {
      labelLists().then(res => {
        let { code, data } = res
        if (code == 200) {
          if (Array.isArray(data)) {
            data = this.handlerCascaderData(data, 'lableName', 'lableId')
            this.treeLabelData = data
          }
        }
      })
    },
    closeDetailDialog() {
      this.detailPage = {
        'currentPage': 1,
        'pageSize': 10,
        'total': 0
      }
      this.dialogTableData = []
    },
    down(row) {
      //  this.statType = mode
      // this.date =  monthValue

      this.$confirm('是否确认导出数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          return downloadCustomerInfo(row)
        })
        .then((response) => {
          this.download(response.msg)
        })
    },
    // 删除
    dele(row) {
      deleteCsmMulti({ id: row.id }).then(res => {
        const { code, data } = res
        if (code == 200) {
          this.query()
        }
      })
    },
    queryList() {
      this.page.current = 1
      this.query()
    },
    query() {
      const { page, formInline } = this
      const { current, size } = page
      const { gridId = [], timeRange = [] } = formInline
      const gridObj = this.getAllGridPathObj(gridId)
      console.log('gridObj===>', gridObj)
      const params = Object.assign({}, formInline, { pageSize: size, currentPage: current, startTime: '', endTime: '', customerCategory: this.impActiveName }, { city: gridObj.city || '', district: gridObj.district || '', grid: gridObj.grid || '' })
      if (timeRange && timeRange.length && Array.isArray(timeRange)) {
        params.startTime = tool.formatterDate(timeRange[0], 'yyyy-MM-dd')
        params.endTime = tool.formatterDate(timeRange[1], 'yyyy-MM-dd')
      }
      delete params.gridId
      delete params.timeRange
      this.tableLoading = true
      queryCustomerPageList(params).then(res => {
        const { code, data } = res
        if (code == 200) {
          const { records, total, size, current } = data || { records: [] }
          this.tableDatas = records
          this.page = Object.assign(this.page, { total, size, current })
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    resetForm(formName) {
      this.page.total = 0
      this.page.size = 10
      this.page.current = 1
      this.formInline.timeRange = []
      this.formInline.gridId = []
      this.$refs[formName].resetFields()
      this.queryList()
    },

    sizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.page.size = val
      this.page.current = 1

      this.query()
    },
    pageCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.page.current = val
      this.query()
    },
    detailSizeChange() {
      this.getDetailTableList()
    },
    detailPageCurrentChange() {
      this.getDetailTableList()
    },
    // 获取详情列表
    getDetailTableList() {
      const { activeRow, detailPage } = this

      const params = Object.assign({}, { servNumber: activeRow.servNumber }, detailPage)
      this.detailTableLoading = true
      viewSingleCustomerList(params).then(res => {
        const { code, data } = res
        const { records, total } = data
        if (code == 200) {
          this.dialogTableData = records || []
          this.detailPage.total = total
        }
      }).finally(() => {
        this.detailTableLoading = false
      })
    },
    // 获取所有网格
    getCascaderOptions() {
      getAllGrides().then(res => {
        const { data } = res
        if (Array.isArray(data) && data.length) {
          const arr = this.handlerCascaderData(data, 'cityName', 'cityId')
          this.optionsCascader = arr
          this.handlerCascaderDataExpand(arr)
        }
      })
    },
    // 处理级联数据 为每一级添加label value
    handlerCascaderData(arr, labelkey, valuekey) {
      if (Array.isArray(arr) && arr.length) {
        arr.forEach(i => {
          i.label = i[labelkey]
          i.value = i[valuekey]
          if (i.hasOwnProperty('lastStage')) {
            i.disabled = i.lastStage == 'N'
          }
          if (i && i.children && i.children.length) {
            this.handlerCascaderData(i.children, labelkey, valuekey)
          } else {
            delete i.children
          }
        })
      }
      return arr
    },
    // 处理网格
    getAllGridPathObj(keyarr) {
      const { optionsCascaderExpand } = this
      const len = keyarr.length
      const obj = {
        city: null,
        cityName: null,
        district: null,
        districtName: null,
        grid: null,
        gridName: null
      }
      let tar = null
      if (len) {
        const lastkey = keyarr[len - 1]

        tar = optionsCascaderExpand.filter((i) => {
          if (i.value == lastkey) {
            return true
          }
          return false
        })

        tar = tar[0]
        const level = tar.level
        // 后端反馈只能传中文
        if (level == 1) {
          obj.city = tar.label
          obj.cityName = tar.value
        } else if (level == 2) {
          obj.district = tar.label
          obj.districtName = tar.value
        } else if (level == 3) {
          obj.grid = tar.label
          obj.gridName = tar.value
        }
      }

      return obj
    },
    handlerCascaderDataExpand(arr) {
      if (Array.isArray(arr) && arr.length) {
        arr.forEach(i => {
          const x = {
            label: i.label,
            value: i.value,
            level: i.level
          }
          this.optionsCascaderExpand.push(x)
          if (i.children && i.children.length) {
            this.handlerCascaderDataExpand(i.children)
          } else {
            delete i.children
          }
        })
      }
    }

  }
}
</script>
<style lang="scss" scoped>
  .el-cascader {
        :deep(.el-icon-arrow-down:before ) {
          content: "\E6E1";
        }
       :deep(.el-icon-arrow-down) {
         transform: rotate(180deg);
       }
       :deep(.is-reverse.el-icon-arrow-down) {
         transform: rotate(0deg);
       }

  }
  .optionbtnbox{
  display: flex;
  >div{
    flex:1;
    // width:33.3%;
    span{
      cursor: pointer;
      &.coloryellow{
        color:#FF9900;
      }
    }
  }
}
.la{
    font-size: 14px;
    text-align: right;
    color: #262626;
    letter-spacing: -0.27px;
    padding-right: 5px;
    font-weight: bold;
}
.vl{
   font-weight: bold;
}

:deep(.el-dialog__header) {
  padding:8px 10px;
  line-height: 20px;
  text-align: center;
  color:#262626;
  background:#f5f5f5;
  font-size: 20px;
}
:deep(.el-dialog__headerbtn) {
  top:12px;
  line-height: 20px;

}
</style>
